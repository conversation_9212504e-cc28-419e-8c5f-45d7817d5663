
# Bug修复与功能增强方案：重复POI及数据不全问题

## 1. 问题描述 (Problem Description)

### 1.1. 现象一：每日行程POI重复
- **具体表现**: 系统生成的旅行计划中，连续多天的行程内容完全一致。例如，第二天的景点、餐厅等POI列表是第一天POI的精确拷贝。
- **日志证据**:
  ```log
  {"timestamp": "...", "message": "[task_...] 最终发送行程更新: day=1, activity=上海世纪公园"}
  {"timestamp": "...", "message": "[task_...] 发送第2天的3个活动"}
  {"timestamp": "...", "message": "[task_...] 最终发送行程更新: day=2, activity=上海世纪公园"}
  ```

### 1.2. 现象二：POI展示信息不全
- **具体表现**: 最终生成的行程中，每个POI仅有基础的名称和位置，缺少用户体验所必需的丰富信息，如**图片、评分、一句话介绍、联系电话**等。这与 `旅游搭子UI.md` 中定义的现代化UI组件要求不符。

## 2. 根本原因分析 (Root Cause Analysis)

### 2.1. POI重复的根源：错误的“预分配”逻辑
问题的核心在于 `src/agents/travel_planner_lg/nodes.py` 的 `run_icp_planning` 函数中采用了**错误的“静态预分配”逻辑**，而不是真正的**“动态迭代”规划**。

- **当前错误流程**:
  1.  **一次性获取**: 在规划开始时，系统搜集所有候选POI。
  2.  **静态分配**: 将同一组高质量的POI**复制并分配**给未来的每一天。
  3.  **重复劳动**: 后续的每日规划循环，只是在基于这份重复的列表进行路线优化，导致结果必然重复。

这种实现方式完全违背了ICP（迭代式上下文规划）的核心思想，即Agent在规划下一步时，并未参考“已经规划了什么”这个最重要的上下文。

### 2.2. POI信息不全的根源：单一的工具调用
- **当前错误流程**:
  1.  Agent在规划时仅调用了`search_poi`这一个基础工具。
  2.  该工具只返回了POI的基础信息（ID, 名称, 位置）。
  3.  缺少后续的、针对性的**详细信息获取**工具调用链，如获取详情、获取图片等。

## 3. 修复与增强方案 (Solution)

我们将通过重构`run_icp_planning`的循环逻辑，并丰富工具调用链来同时解决以上两个问题。

### 3.1. 核心逻辑重构：引入动态决策POI池
我们必须抛弃“预分配”模式，转为真正的动态决策。

1.  **建立主POI池 (Master POI Pool)**: 在规划开始时，获取所有候选POI，形成一个待规划的“主POI池”。
2.  **改造每日规划循环**:
    -   **输入上下文**: 在为第 `N` 天规划时，必须将**前 `N-1` 天已经规划好的行程** (`state.daily_plans`) 作为关键上下文信息输入给LLM。
    -   **动态决策**: LLM（规划模型 `glm-z1-flash`）的任务是：
        a.  理解已经去过哪些地方。
        b.  从“主POI池”中，挑选出**新的、未被使用过的、且与当天行程主题匹配**的POI。
    -   **消耗POI池**: 一旦一个POI被选定并加入当天的行程，就必须从“主POI池”中**移除**，确保它不会在后续的日子里被再次选择。
3.  **地理连续性**: 在为第 `N` (>1) 天选择第一个POI时，可以把第 `N-1` 天最后一个POI的位置作为参考，优先选择附近的POI，确保行程的地理连续性。

### 3.2. POI数据丰富化流程
为了获取符合 `旅游搭子UI.md` 要求的`EnrichedPOI`数据，必须在ICP循环内部，为**每一个选定的POI**执行一个标准的、多步骤的**“数据丰富化”**流程。

**当LLM决定安排某个POI（如“故宫博物院”）后，它必须依次触发以下Action Tools调用**：

1.  **获取详情 `get_poi_details(poi_id)`**: 调用此工具以获取`rating`（评分）、`phone_number`（电话）、`address`（地址）等结构化数据。
2.  **获取图片 `get_poi_images(poi_id)`**: 调用此工具以获取`image_urls`（图片链接列表）。
3.  **AI生成摘要 `generate_poi_summary`**: 在获取完所有API数据后，再次调用LLM，将POI的类型、用户偏好、评分等信息传给它，让它完成最关键的创造性工作：
    -   生成`introduction`（一句话亮点介绍）。
    -   根据行程上下文分配合理的`suggested_time`（建议访问时间）。

最终，将所有信息组装成一个`EnrichedPOI`对象，存入`daily_plans`状态中，并通过`ITINERARY_UPDATE`事件推送给前端。

## 4. 关键代码修改点 (Key Code Modification Points)

### 4.1. `src/agents/travel_planner_lg/nodes.py`
- **重构 `run_icp_planning` 函数**:
    - 移除所有POI预分配逻辑 (`_distribute_pois_to_days`)。
    - 实现新的每日规划循环，该循环会管理一个`remaining_pois`列表。
    - 在循环中，将`state.daily_plans`（已规划行程）和`remaining_pois`（剩余POI）传给LLM进行决策。
    - 成功规划一个POI后，要从`remaining_pois`列表中将其删除。
    - 严格执行3.2节中描述的“数据丰富化”工具调用链。

### 4.2. `src/tools/travel_planner/amap_poi_tools.py`
- **验证并补充Action Tools**: 必须确保以下三个工具已实现并注册到`UnifiedToolRegistry`：
  - `search_poi(...)`: 已存在，作为获取候选POI的第一步。
  - `get_poi_details(poi_id: str)`: **(待确认/补充)** 获取POI的详细信息。
  - `get_poi_images(poi_id: str)`: **(待确认/补充)** 获取POI的高清图片。

### 4.3. `src/prompts/travel_planner/icp_planner.md` (或类似Prompt)
- **更新Prompt指令**: 必须更新指导ICP规划的Prompt，明确指示LLM：
  - “你收到的输入中包含`already_planned_pois`和`available_pois`。”
  - “你的任务是从`available_pois`中选择新的、未被规划的POI。”
  - “在选择后，你需要依次调用`get_poi_details`和`get_poi_images`来丰富其信息。”
  - “最后，你需要为这个POI生成介绍和建议时间。”

## 5. 规划模型配置 (Planning Model Configuration)

根据要求，我们将使用 `glm-z1-flash` 作为核心的规划与思考模型。

- **配置文件**: `config/default.yaml`
- **修改项**:
  ```yaml
  reasoning_llm:
    provider: zhipu
    model: "glm-z1-flash" # 确保规划模型设置为此模型
  ```

## 6. 预期效果 (Expected Outcome)

1.  **问题修复**: 每日行程内容将完全不同，规划结果合理且多样。
2.  **体验提升**: 每个POI都将包含图片、评分、介绍等丰富信息，符合前端UI设计要求，用户体验大幅改善。
3.  **架构遵从**: Agent的规划逻辑将真正符合ICP（迭代式上下文规划）的动态、智能原则。

## 7. 期望的推送事件与前端效果 (Expected Push Events & Frontend Effect)

本章节定义了修复完成后，后端应如何通过SSE事件将丰富且不重复的行程信息推送给前端，以匹配 `旅游搭子UI.md` 的设计要求。

### 7.1. POI丰富化过程的实时推送
对于每一个被选中的POI，前端界面都应该能实时地、透明地展示其“数据丰富化”的全过程。这将通过一系列有序的SSE事件来实现：

1.  **Agent决策日志**:
    -   **事件**: `PLANNING_LOG`
    -   **内容**: `{"message": "已选定「故宫博物院」，正在为其获取详细信息..."}`
2.  **获取详情**:
    -   **事件**: `tool_start` -> `tool_end`
    -   **内容**: `{"tool_name": "get_poi_details", ...}`
3.  **获取图片**:
    -   **事件**: `tool_start` -> `tool_end`
    -   **内容**: `{"tool_name": "get_poi_images", ...}`
4.  **AI润色日志**:
    -   **事件**: `PLANNING_LOG`
    -   **内容**: `{"message": "正在为「故宫博物院」生成亮点介绍和建议时间..."}`
5.  **最终行程更新**:
    -   **事件**: `ITINERARY_UPDATE`
    -   **内容**: 包含该POI所有丰富信息的完整Payload。

这个过程让用户能清晰地看到AI正在为他“努力工作”，极大地提升了信任感和体验。

### 7.2. `ITINERARY_UPDATE` 事件的最终Payload
修复后，每一次 `ITINERARY_UPDATE` 事件中 `data.activity` 对象的结构必须是信息完整的，示例如下：

```json
{
  "event": "ITINERARY_UPDATE",
  "data": {
    "day": 1,
    "activity": {
      "poi_id": "B000A8UIN8",
      "name": "故宫博物院",
      "poi_type": "attraction",
      "location": "116.397029,39.917839",
      "introduction": "明清两代的皇家宫殿，感受中华五千年的历史脉搏。",
      "suggested_time": "上午 09:00 - 12:00",
      "rating": 4.9,
      "phone_number": "************",
      "image_urls": ["http://.../gugong1.jpg", "http://.../gugong2.jpg"]
    },
    "timestamp": "2023-07-15T10:30:00.123Z"
  }
}
```

### 7.3. 修复后的推送序列示例
最关键的是，不同日期的 `ITINERARY_UPDATE` 事件流中，推送的活动内容**必须是不同的**（除非是多日通票或连续入住的酒店等合理情况）。

#### 第1天事件流 (Day 1 Event Stream)
```
...
--> PLANNING_LOG: "开始规划第1天的行程..."
--> ITINERARY_UPDATE: { day: 1, activity: { name: "故宫博物院", ... } }
--> ITINERARY_UPDATE: { day: 1, activity: { name: "四季民福烤鸭店", ... } }
...
```

#### 第2天事件流 (Day 2 Event Stream)
```
...
--> PLANNING_LOG: "第1天规划完成。开始规划第2天的行程..."
--> ITINERARY_UPDATE: { day: 2, activity: { name: "颐和园", ... } }         // <-- 不同的景点
--> ITINERARY_UPDATE: { day: 2, activity: { name: "海底捞火锅", ... } }       // <-- 不同的餐厅
...
```
这个清晰的、不重复的、信息丰富的事件流，是本次修复需要达成的最终目标。

## 8. 前端承接与代码实现 (Frontend Integration & Implementation)

为了让前端能正确消费和展示修复后生成的丰富POI数据，需要对前端的JavaScript代码进行相应修改。

-   **目标文件**: `static/js/app-v3-refactored.js`
-   **修改核心**: 负责处理`ITINERARY_UPDATE` SSE事件的JavaScript函数。

该函数需要被修改，以动态创建一个包含所有新信息的HTML卡片，并将其插入到对应“天”的容器中。

### 8.1. POI卡片HTML动态生成模板
前端JavaScript在收到`ITINERARY_UPDATE`事件后，应根据其`data.activity`内容，动态生成类似下方结构的HTML，并插入到`#dailyItinerary`下的每日容器中。

```html
<!-- 这是由JS动态生成的单个POI卡片模板 -->
<div class="poi-card">
    <div class="poi-image-container">
        <!-- 优先使用第一张图片作为主图 -->
        <img src="${activity.image_urls[0]}" alt="${activity.name}" class="poi-main-image">
        <span class="poi-type-badge">${activity.poi_type}</span>
    </div>
    <div class="poi-content">
        <div class="poi-header">
            <h6 class="poi-name">${activity.name}</h6>
            <div class="poi-rating">
                <i class="bi bi-star-fill"></i>
                <span>${activity.rating}</span>
            </div>
        </div>
        <p class="poi-introduction">${activity.introduction}</p>
        <div class="poi-details">
            <div class="poi-detail-item">
                <i class="bi bi-clock"></i>
                <span>${activity.suggested_time}</span>
            </div>
            <div class="poi-detail-item">
                <i class="bi bi-telephone"></i>
                <span>${activity.phone_number || '暂无电话'}</span>
            </div>
        </div>
    </div>
</div>
```
*开发者需要将`${...}`占位符替换为从SSE事件中获取的真实数据。*

